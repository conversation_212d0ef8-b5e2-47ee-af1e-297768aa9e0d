<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    @ok="handleSubmit"
    :wrap-class-name="$style['union-info-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />

    <BasicTable
      @register="registerTable"
      v-if="disabled"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <UnionCadreModal
      @register="registerCadre"
      width="60%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal, useModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalForm, pagedColumns, pagedFormSchema } from './data';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { find, paged } from '@/api/cadre';
import UnionCadreModal from '@/views/union/cadre/UnionCadreModal.vue';
import { join, split } from 'lodash-es';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.companyName || ''}--详情`
      : `编辑${unref(record)?.companyName || ''}`
    : '新增工会';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalForm();
});

const [registerCadre, { openModal }] = useModal();

const [registerForm, { resetFields, validate, setProps, setFieldsValue }] = useForm({
  labelWidth: 200,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      ssqy: data.record.ssqy ? split(data.record.ssqy, ',') : ['410000', '410100', '410171'],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  api: paged,
  columns: pagedColumns(),
  formConfig: {
    labelWidth: 120,
    schemas: pagedFormSchema(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 6 },
  },
  immediate: false,
  maxHeight: 500,
  useSearchForm: true,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: false,
  },
});

function handleView(row) {
  find({ id: row.cadreId }).then(({ code, data }) => {
    code === 200 && openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { ssqy, ...values } = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        ssqy: join(ssqy || ['410000', '410100', '410171'], ','),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.union-info-modal {
  :global {
    .ant-modal-body {
      max-height: 70vh;
      overflow: auto;
    }
  }
}
</style>
