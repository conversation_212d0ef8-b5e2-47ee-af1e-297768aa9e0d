<template>
  <div :class="$style['union-cadre']">
    <a-row>
      <a-col :span="4">
        <UnionNextLevel @selectInfo="handleNext"></UnionNextLevel>
      </a-col>
      <a-col :span="20">
        <PageWrapper :title="`${companyName}干部信息`">
          <BasicTable @register="registerTable">
            <template #toolbar>
              <a-button
                type="primary"
                :loading="loading"
                @click="handleAdd"
                >新增干部</a-button
              >
            </template>
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'fa6-solid:pen-to-square',
                      label: '编辑',
                      type: 'primary',
                      onClick: handleEdit.bind(null, record),
                    },
                    {
                      icon: 'carbon:task-view',
                      label: '详情',
                      type: 'default',
                      onClick: handleView.bind(null, record),
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
        </PageWrapper>
      </a-col>
    </a-row>
    <UnionCadreModal
      @register="registerModal"
      width="60%"
      :canFullscreen="false"
    />
  </div>
</template>

<script lang="ts" setup>
import { Row, Col } from 'ant-design-vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { pagedFormSchema, pagedColumns } from './data';
import { find, paged, importCadreInfo } from '@/api/cadre';
import { useUserStore } from '@/store/modules/user';
import UnionNextLevel from '../UnionNextLevel/index.vue';
import { PageWrapper } from '@/components/Page';
import { ref, watch } from 'vue';
import { useModal } from '@/components/Modal';
import UnionCadreModal from '@/views/union/cadre/UnionCadreModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const ARow = Row;
const ACol = Col;

const { createErrorModal } = useMessage();

const userStore = useUserStore();

const loading = ref(false);

const companyId = ref(userStore.getUserInfo.companyId);

const companyName = ref(userStore.getUserInfo.companyName || '');

const [registerModal, { openModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'cadreId',
  api: paged,
  columns: pagedColumns(),
  formConfig: {
    labelWidth: 120,
    schemas: pagedFormSchema(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 24 },
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: 'right',
  },
});

// 新增
function handleAdd() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record) {
  find({ openId: record.openId }).then(res => {
    if (res.code === 200) {
      openModal(true, { record: res.data, disabled: false, isUpdate: true });
    } else {
      createErrorModal({ content: `查看详情失败! ${res.message}` });
    }
  });
}

async function handleView(row) {
  find({ openId: row.openId }).then(({ code, data }) => {
    code === 200 && openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

function handleNext({ companyId: id, companyName: name }) {
  companyId.value = id;
  companyName.value = name;
}

watch(companyId, () => {
  reload();
});
</script>

<style lang="less" module>
.union-cadre {
  :global {
    background-color: #fff;

    .ant-form {
      @apply px-6px;
    }

    .ant-page-header {
      @apply !py-0 !pl-36px !pb-0;

      span {
        @apply !text-[#2172f1] font-bold;
      }
    }
  }
}
</style>
