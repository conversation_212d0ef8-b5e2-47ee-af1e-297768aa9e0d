import { cloneDeep, map, toNumber } from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const getColumns = (): BasicColumn[] => {
  return [
    {
      title: '序号',
      dataIndex: 'orderId',
    },
    {
      title: '姓名',
      dataIndex: 'cadreName',
    },
    {
      title: '联系电话',
      dataIndex: 'contractPhone',
    },
    {
      title: '身份证号',
      dataIndex: 'cadreIdentity',
    },
    {
      title: '性别',
      dataIndex: 'gender',
    },
    {
      title: '民族',
      dataIndex: 'nation',
    },
    {
      title: '干部类型',
      dataIndex: 'cadreType',
    },
    {
      title: '学历',
      dataIndex: 'education',
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '职务名称',
      dataIndex: 'postName',
    },
    {
      title: '政治面貌',
      dataIndex: 'politics',
    },
    {
      title: '工作单位',
      dataIndex: 'workCompanyName',
    },
  ];
};

export const pagedColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '姓名',
      dataIndex: 'cadreName',
    },
    {
      title: '联系电话',
      dataIndex: 'contractPhone',
    },
    {
      title: '身份证号',
      dataIndex: 'cadreIdentity',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`xb_${record.nation}`)?.dictName;
        return name || record.nation;
      },
    },
    {
      title: '民族',
      dataIndex: 'nation',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`nation_${record.nation}`)?.dictName;
        return name || record.nation;
      },
    },
    {
      title: '干部类型',
      dataIndex: 'cadreType',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`gblx_${record.cadreType}`)?.dictName;
        return name || record.cadreType;
      },
    },
    {
      title: '学历',
      dataIndex: 'education',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(
          `modelEducation_${record.education}`
        )?.dictName;
        return name || record.education;
      },
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '职务名称',
      dataIndex: 'postName',
    },
    {
      title: '政治面貌',
      dataIndex: 'politics',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`politicsState_${record.politics}`)?.dictName;
        return name || record.politics;
      },
    },
    {
      title: '工作单位',
      dataIndex: 'workCompanyName',
    },
  ];
};

export const pagedFormSchema = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'cadreName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contractPhone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'cadreIdentity',
      label: '身份证号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'gender',
      label: '性别',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
    },
    {
      field: 'nation',
      label: '民族',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('nation') || [],
      },
    },
    {
      field: 'cadreType',
      label: '干部类型',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('gblx')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
    {
      field: 'education',
      label: '学历',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('modelEducation')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
    {
      field: 'politics',
      label: '政治面貌',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('politicsState')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: {
        span: 3,
      },
      defaultValue: true,
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'cadreName',
      component: 'Input',
      label: '姓名',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'contractPhone',
      component: 'Input',
      label: '联系电话',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'cadreIdentity',
      component: 'Input',
      label: '身份证号',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'gender',
      component: 'RadioGroup',
      label: '性别',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xb') as RadioGroupChildOption[],
      },
    },
    {
      field: 'nation',
      component: 'Select',
      label: '民族',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('nation'),
      },
    },
    {
      field: 'cadreType',
      component: 'Select',
      label: '干部类型',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('gblx')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
    {
      field: 'education',
      component: 'Select',
      label: '学历',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('modelEducation')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
    {
      field: 'deptName',
      component: 'Input',
      label: '部门名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'postName',
      component: 'Input',
      label: '职务名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'politics',
      component: 'Select',
      label: '政治面貌',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: map(cloneDeep(dictionary.getDictionaryOpt.get('politicsState')), v => ({
          ...v,
          value: toNumber(v.value),
        })),
      },
    },
  ];
};
